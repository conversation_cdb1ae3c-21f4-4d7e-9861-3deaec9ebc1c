import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Briefcase, Building2, ExternalLink } from "lucide-react";
import { experiences } from "@/data/experienceData";

const ExperienceSection = () => {
  const items = experiences || [];

  const handleCompanyClick = (website: string) => {
    if (website) {
      window.open(website, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <section id="experience" className="mobile-section relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-4 w-48 h-48 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-4 w-56 h-56 bg-accent-glow/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="mobile-container mx-auto relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="mobile-heading mb-4">
            <span className="neon-text-glow">Professional Experience</span>
          </h2>
          <p className="mobile-subheading text-muted-foreground">Roles and responsibilities</p>
        </div>

        {/* Mobile-optimized: horizontal scroll with 2 cards per row on mobile, grid on larger screens */}
        <div className="max-w-6xl mx-auto">
          {/* Mobile horizontal scroll layout */}
          <div className="block lg:hidden">
            <div
              className="flex gap-4 overflow-x-auto pb-4 scroll-smooth snap-x snap-mandatory scrollbar-hide"
              role="region"
              aria-label="Professional experience carousel"
            >
              {items.map((exp, idx) => (
                <div key={idx} className="flex-none w-[calc(50%-8px)] min-w-[300px] snap-start">
                  <Card className="glass-card animate-fade-in-up h-full" style={{ animationDelay: `${idx * 100}ms` }}>
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="bg-gradient-primary rounded-full p-2">
                            <Briefcase className="h-4 w-4 text-primary-foreground" />
                          </div>
                          <Badge variant="secondary" className="glass-card text-xs font-semibold">{exp.company}</Badge>
                        </div>
                        {exp.companyWebsite && (
                          <button
                            onClick={() => handleCompanyClick(exp.companyWebsite!)}
                            className="group bg-gradient-primary rounded-full p-2 hover:shadow-glow-strong transition-all duration-300 hover:scale-110"
                            aria-label={`Visit ${exp.company} website`}
                          >
                            <Building2 className="h-4 w-4 text-primary-foreground group-hover:rotate-12 transition-transform duration-300" />
                            <ExternalLink className="h-2 w-2 text-primary-foreground absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </button>
                        )}
                      </div>
                      <CardTitle className="text-lg mb-2 hover:neon-text transition-all duration-300">
                        {exp.role}
                      </CardTitle>
                      <div className="text-xs text-muted-foreground">{exp.duration}</div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <ul className="space-y-2">
                        {exp.responsibilities.map((responsibility, respIdx) => (
                          <li key={respIdx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <span className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span>
                            <span className="flex-1">{responsibility}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Desktop grid layout */}
          <div className="hidden lg:grid lg:grid-cols-2 gap-6 lg:gap-8">
            {items.map((exp, idx) => (
              <Card key={idx} className="glass-card animate-fade-in-up h-full" style={{ animationDelay: `${idx * 100}ms` }}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="bg-gradient-primary rounded-full p-2">
                        <Briefcase className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <Badge variant="secondary" className="glass-card text-xs font-semibold">{exp.company}</Badge>
                    </div>
                    {exp.companyWebsite && (
                      <button
                        onClick={() => handleCompanyClick(exp.companyWebsite!)}
                        className="group bg-gradient-primary rounded-full p-2 hover:shadow-glow-strong transition-all duration-300 hover:scale-110"
                        aria-label={`Visit ${exp.company} website`}
                      >
                        <Building2 className="h-4 w-4 text-primary-foreground group-hover:rotate-12 transition-transform duration-300" />
                        <ExternalLink className="h-2 w-2 text-primary-foreground absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </button>
                    )}
                  </div>
                  <CardTitle className="text-xl sm:text-2xl mb-2 hover:neon-text transition-all duration-300">
                    {exp.role}
                  </CardTitle>
                  <div className="text-xs sm:text-sm text-muted-foreground">{exp.duration}</div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <ul className="space-y-2 list-disc pl-5">
                    {(exp.responsibilities || []).map((item, i) => (
                      <li key={i} className="text-xs sm:text-sm text-muted-foreground">
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
