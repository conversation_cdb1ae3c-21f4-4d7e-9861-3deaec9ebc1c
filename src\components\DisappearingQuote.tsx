import { useEffect, useState } from 'react';

const DisappearingQuote = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [opacity, setOpacity] = useState(1);

  useEffect(() => {
    // <PERSON>le scroll to fade quote when not on home screen (similar to floating icons)
    const handleScroll = () => {
      const heroSection = document.getElementById('hero');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        const heroHeight = heroSection.offsetHeight;
        const scrollProgress = Math.max(0, Math.min(1, (heroHeight - rect.bottom) / (heroHeight * 0.3)));
        
        // Smooth fade-out as user scrolls past hero section
        const newOpacity = Math.max(0, 1 - scrollProgress);
        setOpacity(newOpacity);
        
        // Hide completely when opacity is very low to improve performance
        const isHeroVisible = rect.bottom > 50 && newOpacity > 0.1;
        setIsVisible(isHeroVisible);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Check initial state

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <div 
      className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-30 transition-opacity duration-500 pointer-events-none"
      style={{ opacity }}
    >
      <div className="glass-card px-6 py-3 backdrop-blur-md">
        <p className="text-lg sm:text-xl lg:text-2xl font-medium neon-text-glow text-center whitespace-nowrap">
          Transforming data into insight, code into impact
        </p>
      </div>
    </div>
  );
};

export default DisappearingQuote;
